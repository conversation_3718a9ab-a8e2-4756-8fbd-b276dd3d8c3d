# 燕友圈榜单系统 - 完整API文档

## 📋 目录

- [基础信息](#基础信息)
- [认证说明](#认证说明)
- [通用响应格式](#通用响应格式)
- [错误码说明](#错误码说明)
- [API端点](#api端点)
  - [认证模块](#认证模块)
  - [榜单管理](#榜单管理)
  - [用户管理](#用户管理)
  - [赞助商管理](#赞助商管理)
  - [文件上传管理](#文件上传管理)
  - [系统健康检查](#系统健康检查)

## 🔧 基础信息

- **基础URL**: `http://localhost:8000` (开发环境) / `https://your-domain.com` (生产环境)
- **API版本**: v1
- **API前缀**: `/api/v1`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 🔐 认证说明

### Bearer Token认证

大部分API端点需要Bearer Token认证。在请求头中添加：

```http
Authorization: Bearer <your_access_token>
```

### 获取Token

通过登录接口获取访问令牌：
- 用户名密码登录：`POST /api/v1/auth/login`
- 微信登录：`POST /api/v1/auth/wechat-login`

### Token刷新

Token过期时可通过刷新接口获取新Token：
- `POST /api/v1/auth/refresh`

## 📄 通用响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

### 分页响应

```json
{
  "code": 200,
  "message": "获取数据成功",
  "data": {
    "items": [
      // 数据列表
    ],
    "total": 100,
    "page": 1,
    "size": 10,
    "pages": 10
  }
}
```

### 错误响应

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null
}
```

## ❌ 错误码说明

| 错误码 | 说明 | 常见原因 |
|--------|------|----------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求错误 | 参数格式错误、必填参数缺失 |
| 401 | 未授权 | Token无效、未登录 |
| 403 | 权限不足 | 无操作权限 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 422 | 参数验证失败 | 参数类型错误、格式不正确 |
| 500 | 服务器错误 | 内部服务器错误 |

## 📊 API接口总览

### 认证模块 (5个接口)

| 接口 | 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|------|----------|
| 用户登录 | POST | `/api/v1/auth/login` | 用户名密码登录 | 无 |
| 微信登录 | POST | `/api/v1/auth/wechat-login` | 微信授权登录 | 无 |
| 获取当前用户信息 | GET | `/api/v1/auth/me` | 获取当前登录用户信息 | Bearer Token |
| 刷新Token | POST | `/api/v1/auth/refresh` | 刷新访问令牌 | Bearer Token |
| 用户登出 | POST | `/api/v1/auth/logout` | 用户登出，使Token失效 | Bearer Token |

### 榜单管理模块 (8个接口)

| 接口 | 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|------|----------|
| 获取榜单列表 | GET | `/api/v1/rankings` | 获取榜单列表，支持分页和筛选 | Bearer Token |
| 创建榜单 | POST | `/api/v1/rankings` | 创建新榜单 | Bearer Token (管理员) |
| 获取榜单详情 | GET | `/api/v1/rankings/{id}` | 获取指定榜单的详细信息 | Bearer Token |
| 更新榜单 | PUT | `/api/v1/rankings/{id}` | 更新榜单信息 | Bearer Token (管理员) |
| 删除榜单 | DELETE | `/api/v1/rankings/{id}` | 删除指定榜单 | Bearer Token (管理员) |
| 获取榜单明细 | GET | `/api/v1/rankings/{id}/details` | 获取指定榜单的明细记录 | Bearer Token |
| 添加榜单明细 | POST | `/api/v1/rankings/{id}/details` | 为榜单添加明细记录 | Bearer Token (管理员) |
| 更新榜单状态 | PUT | `/api/v1/rankings/{id}/status` | 更新榜单状态 | Bearer Token (管理员) |

### 用户管理模块 (5个接口)

| 接口 | 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|------|----------|
| 获取用户列表 | GET | `/api/v1/users` | 获取用户列表，支持分页和筛选 | Bearer Token (管理员) |
| 创建用户 | POST | `/api/v1/users` | 创建新用户 | Bearer Token (管理员) |
| 获取用户详情 | GET | `/api/v1/users/{id}` | 获取指定用户的详细信息 | Bearer Token (管理员或本人) |
| 更新用户信息 | PUT | `/api/v1/users/{id}` | 更新用户信息 | Bearer Token (管理员或本人) |
| 删除用户 | DELETE | `/api/v1/users/{id}` | 删除指定用户 | Bearer Token (管理员) |

### 赞助商管理模块 (9个接口)

| 接口 | 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|------|----------|
| 获取赞助商列表 | GET | `/api/v1/sponsors` | 获取赞助商列表，支持分页和筛选 | Bearer Token |
| 获取活跃赞助商列表 | GET | `/api/v1/sponsors/active` | 获取活跃赞助商列表 | Bearer Token |
| 获取赞助商详情 | GET | `/api/v1/sponsors/{id}` | 获取指定赞助商的详细信息 | Bearer Token |
| 创建赞助商 | POST | `/api/v1/sponsors` | 创建新赞助商 | Bearer Token (管理员) |
| 更新赞助商信息 | PUT | `/api/v1/sponsors/{id}` | 更新赞助商信息 | Bearer Token (管理员) |
| 删除赞助商 | DELETE | `/api/v1/sponsors/{id}` | 删除指定赞助商 | Bearer Token (管理员) |
| 切换赞助商激活状态 | PUT | `/api/v1/sponsors/{id}/toggle-status` | 切换赞助商的激活状态 | Bearer Token (管理员) |
| 更新赞助商排序 | PUT | `/api/v1/sponsors/{id}/sort-order` | 更新赞助商排序顺序 | Bearer Token (管理员) |

### 文件上传管理模块 (3个接口)

| 接口 | 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|------|----------|
| 上传临时Excel文件 | POST | `/api/v1/upload/excel/upload-temp` | 上传Excel文件到临时目录 | Bearer Token |
| 解析Excel文件 | POST | `/api/v1/upload/excel/parse` | 解析榜单明细Excel文件 | Bearer Token |
| 下载Excel模板 | GET | `/api/v1/upload/excel/template` | 下载榜单明细Excel模板文件 | Bearer Token |

### 系统健康检查模块 (2个接口)

| 接口 | 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|------|----------|
| 根路径检查 | GET | `/` | 系统根路径健康检查 | 无 |
| 健康检查 | GET | `/health` | 系统健康状态检查 | 无 |

**总计：32个API接口**
|------|------|------|------|----------|
| 用户登录 | POST | `/api/v1/auth/login` | 用户名密码登录 | 无 |
| 微信登录 | POST | `/api/v1/auth/wechat-login` | 微信授权登录 | 无 |
| 获取当前用户信息 | GET | `/api/v1/auth/me` | 获取当前登录用户信息 | Bearer Token |
| 刷新Token | POST | `/api/v1/auth/refresh` | 刷新访问令牌 | Bearer Token |
| 用户登出 | POST | `/api/v1/auth/logout` | 用户登出，使Token失效 | Bearer Token |

### 榜单管理模块 (8个接口)

| 接口 | 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|------|----------|
| 获取榜单列表 | GET | `/api/v1/rankings` | 获取榜单列表，支持分页和筛选 | Bearer Token |
| 创建榜单 | POST | `/api/v1/rankings` | 创建新榜单 | Bearer Token (管理员) |
| 获取榜单详情 | GET | `/api/v1/rankings/{id}` | 获取指定榜单的详细信息 | Bearer Token |
| 更新榜单 | PUT | `/api/v1/rankings/{id}` | 更新榜单信息 | Bearer Token (管理员) |
| 删除榜单 | DELETE | `/api/v1/rankings/{id}` | 删除指定榜单 | Bearer Token (管理员) |
| 获取榜单明细 | GET | `/api/v1/rankings/{id}/details` | 获取指定榜单的明细记录 | Bearer Token |
| 添加榜单明细 | POST | `/api/v1/rankings/{id}/details` | 为榜单添加明细记录 | Bearer Token (管理员) |
| 更新榜单状态 | PUT | `/api/v1/rankings/{id}/status` | 更新榜单状态 | Bearer Token (管理员) |

### 用户管理模块 (5个接口)

| 接口 | 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|------|----------|
| 获取用户列表 | GET | `/api/v1/users` | 获取用户列表，支持分页和筛选 | Bearer Token (管理员) |
| 创建用户 | POST | `/api/v1/users` | 创建新用户 | Bearer Token (管理员) |
| 获取用户详情 | GET | `/api/v1/users/{id}` | 获取指定用户的详细信息 | Bearer Token (管理员或本人) |
| 更新用户信息 | PUT | `/api/v1/users/{id}` | 更新用户信息 | Bearer Token (管理员或本人) |
| 删除用户 | DELETE | `/api/v1/users/{id}` | 删除指定用户 | Bearer Token (管理员) |

### 赞助商管理模块 (9个接口)

| 接口 | 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|------|----------|
| 获取赞助商列表 | GET | `/api/v1/sponsors` | 获取赞助商列表，支持分页和筛选 | Bearer Token |
| 获取活跃赞助商列表 | GET | `/api/v1/sponsors/active` | 获取活跃赞助商列表 | Bearer Token |
| 获取赞助商详情 | GET | `/api/v1/sponsors/{id}` | 获取指定赞助商的详细信息 | Bearer Token |
| 创建赞助商 | POST | `/api/v1/sponsors` | 创建新赞助商 | Bearer Token (管理员) |
| 更新赞助商信息 | PUT | `/api/v1/sponsors/{id}` | 更新赞助商信息 | Bearer Token (管理员) |
| 删除赞助商 | DELETE | `/api/v1/sponsors/{id}` | 删除指定赞助商 | Bearer Token (管理员) |
| 切换赞助商激活状态 | PUT | `/api/v1/sponsors/{id}/toggle-status` | 切换赞助商的激活状态 | Bearer Token (管理员) |
| 更新赞助商排序 | PUT | `/api/v1/sponsors/{id}/sort-order` | 更新赞助商排序顺序 | Bearer Token (管理员) |

### 文件上传管理模块 (3个接口)

| 接口 | 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|------|----------|
| 上传临时Excel文件 | POST | `/api/v1/upload/excel/upload-temp` | 上传Excel文件到临时目录 | Bearer Token |
| 解析Excel文件 | POST | `/api/v1/upload/excel/parse` | 解析榜单明细Excel文件 | Bearer Token |
| 下载Excel模板 | GET | `/api/v1/upload/excel/template` | 下载榜单明细Excel模板文件 | Bearer Token |

### 系统健康检查模块 (2个接口)

| 接口 | 方法 | 路径 | 描述 | 认证要求 |
|------|------|------|------|----------|
| 根路径检查 | GET | `/` | 系统根路径健康检查 | 无 |
| 健康检查 | GET | `/health` | 系统健康状态检查 | 无 |

**总计：32个API接口**

---

# 🔑 认证模块

## 1. 用户登录

**接口**: `POST /api/v1/auth/login`  
**描述**: 用户名密码登录  
**认证**: 不需要

### 请求参数

```json
{
  "username": "admin",
  "password": "password123"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| username | string | 是 | 用户名，3-50个字符 |
| password | string | 是 | 密码 |

### 响应示例

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "avatar_url": "https://example.com/avatar.jpg",
      "role": "admin",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 2. 微信登录

**接口**: `POST /api/v1/auth/wechat-login`  
**描述**: 微信授权登录  
**认证**: 不需要

### 请求参数

```json
{
  "code": "wx_auth_code_from_frontend"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| code | string | 是 | 微信授权码 |

### 响应示例

```json
{
  "code": 200,
  "message": "微信登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 2,
      "username": null,
      "nickname": "微信用户",
      "avatar_url": "https://wx.qlogo.cn/mmopen/...",
      "wechat_openid": "oXXXXXXXXXXXXXXXXXXXXXXXXX",
      "role": "user",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 3. 获取当前用户信息

**接口**: `GET /api/v1/auth/me`  
**描述**: 获取当前登录用户信息  
**认证**: 需要Bearer Token

### 请求示例

**HTTP请求**：
```http
GET /api/v1/auth/me HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**cURL命令**：
```bash
curl --location --request GET 'http://localhost:8000/api/v1/auth/me' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
--header 'Accept: application/json' \
--header 'Content-Type: application/json' \
--header 'User-Agent: YysRankingSystem/1.0.0' \
--header 'Connection: keep-alive'
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "username": "admin",
    "nickname": "管理员",
    "avatar_url": "https://example.com/avatar.jpg",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "role": "admin",
    "level": "管理员",
    "bio": "系统管理员",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "last_login_at": "2024-01-15T10:30:00Z"
  }
}
```

## 4. 刷新Token

**接口**: `POST /api/v1/auth/refresh`  
**描述**: 刷新访问令牌  
**认证**: 需要Bearer Token

### 请求示例

```http
POST /api/v1/auth/refresh HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "role": "admin"
    }
  }
}
```

## 5. 用户登出

**接口**: `POST /api/v1/auth/logout`  
**描述**: 用户登出，使Token失效  
**认证**: 需要Bearer Token

### 请求示例

```http
POST /api/v1/auth/logout HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

---

# 📊 榜单管理

## 1. 获取榜单列表

**接口**: `GET /api/v1/rankings`  
**描述**: 获取榜单列表，支持分页和筛选  
**认证**: 需要Bearer Token

### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| status | string | 否 | - | 榜单状态：draft/active/completed/cancelled |
| ranking_type | string | 否 | - | 榜单类型：five_person/ten_person |

### 请求示例

```http
GET /api/v1/rankings?page=1&size=10&status=active&ranking_type=five_person HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取榜单列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "第1期5人竞速榜",
        "period": 1,
        "ranking_type": "five_person",
        "start_time": "2024-01-15T09:00:00Z",
        "end_time": "2024-01-15T18:00:00Z",
        "team_size_limit": 5,
        "total_participants": 25,
        "status": "active",
        "created_at": "2024-01-14T10:00:00Z",
        "updated_at": "2024-01-15T08:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

## 2. 创建榜单

**接口**: `POST /api/v1/rankings`
**描述**: 创建新榜单
**认证**: 需要Bearer Token（管理员权限）

### 请求参数

```json
{
  "name": "第2期10人竞速榜",
  "period": 2,
  "ranking_type": "ten_person",
  "start_time": "2024-01-20T09:00:00Z",
  "end_time": "2024-01-20T18:00:00Z",
  "team_size_limit": 10
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 是 | 榜单名称，1-200个字符 |
| period | integer | 是 | 期数，必须大于0 |
| ranking_type | string | 是 | 榜单类型：five_person/ten_person |
| start_time | string | 是 | 开始时间，ISO 8601格式 |
| end_time | string | 是 | 结束时间，ISO 8601格式 |
| team_size_limit | integer | 是 | 组队人数限制，1-20人 |

### 响应示例

```json
{
  "code": 200,
  "message": "创建榜单成功",
  "data": {
    "id": 2,
    "name": "第2期10人竞速榜",
    "period": 2,
    "ranking_type": "ten_person",
    "start_time": "2024-01-20T09:00:00Z",
    "end_time": "2024-01-20T18:00:00Z",
    "team_size_limit": 10,
    "total_participants": 0,
    "status": "draft",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

## 3. 获取榜单详情

**接口**: `GET /api/v1/rankings/{ranking_id}`
**描述**: 获取指定榜单的详细信息
**认证**: 需要Bearer Token

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| ranking_id | integer | 是 | 榜单ID |

### 请求示例

```http
GET /api/v1/rankings/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取榜单详情成功",
  "data": {
    "id": 1,
    "name": "第1期5人竞速榜",
    "period": 1,
    "ranking_type": "five_person",
    "start_time": "2024-01-15T09:00:00Z",
    "end_time": "2024-01-15T18:00:00Z",
    "team_size_limit": 5,
    "total_participants": 25,
    "status": "active",
    "created_at": "2024-01-14T10:00:00Z",
    "updated_at": "2024-01-15T08:00:00Z",
    "details": [
      {
        "id": 1,
        "ranking_id": 1,
        "rank_start": 1,
        "rank_end": 5,
        "completion_time": "02:30:45",
        "completion_seconds": 9045,
        "participant_count": 5,
        "team_info": "{\"team_name\":\"闪电队\",\"members\":[\"张三\",\"李四\",\"王五\",\"赵六\",\"钱七\"]}",
        "created_at": "2024-01-15T11:30:45Z",
        "updated_at": "2024-01-15T11:30:45Z"
      }
    ]
  }
}
```

## 4. 更新榜单

**接口**: `PUT /api/v1/rankings/{ranking_id}`
**描述**: 更新榜单信息
**认证**: 需要Bearer Token（管理员权限）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| ranking_id | integer | 是 | 榜单ID |

### 请求参数

```json
{
  "name": "第1期5人竞速榜（更新）",
  "start_time": "2024-01-15T10:00:00Z",
  "end_time": "2024-01-15T19:00:00Z",
  "status": "active"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 否 | 榜单名称，1-200个字符 |
| start_time | string | 否 | 开始时间，ISO 8601格式 |
| end_time | string | 否 | 结束时间，ISO 8601格式 |
| status | string | 否 | 榜单状态：draft/active/completed/cancelled |

### 响应示例

```json
{
  "code": 200,
  "message": "更新榜单成功",
  "data": {
    "id": 1,
    "name": "第1期5人竞速榜（更新）",
    "period": 1,
    "ranking_type": "five_person",
    "start_time": "2024-01-15T10:00:00Z",
    "end_time": "2024-01-15T19:00:00Z",
    "team_size_limit": 5,
    "total_participants": 25,
    "status": "active",
    "created_at": "2024-01-14T10:00:00Z",
    "updated_at": "2024-01-15T12:00:00Z"
  }
}
```

## 5. 删除榜单

**接口**: `DELETE /api/v1/rankings/{ranking_id}`
**描述**: 删除指定榜单
**认证**: 需要Bearer Token（管理员权限）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| ranking_id | integer | 是 | 榜单ID |

### 请求示例

```http
DELETE /api/v1/rankings/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "删除榜单成功",
  "data": null
}
```

## 6. 获取榜单明细

**接口**: `GET /api/v1/rankings/{ranking_id}/details`
**描述**: 获取指定榜单的明细记录
**认证**: 需要Bearer Token

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| ranking_id | integer | 是 | 榜单ID |

### 请求示例

```http
GET /api/v1/rankings/1/details HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取榜单明细成功",
  "data": [
    {
      "id": 1,
      "ranking_id": 1,
      "rank_start": 1,
      "rank_end": 5,
      "completion_time": "02:30:45",
      "completion_seconds": 9045,
      "participant_count": 5,
      "team_info": "{\"team_name\":\"闪电队\",\"members\":[\"张三\",\"李四\",\"王五\",\"赵六\",\"钱七\"]}",
      "created_at": "2024-01-15T11:30:45Z",
      "updated_at": "2024-01-15T11:30:45Z"
    }
  ]
}
```

## 7. 添加榜单明细

**接口**: `POST /api/v1/rankings/{ranking_id}/details`
**描述**: 为榜单添加明细记录
**认证**: 需要Bearer Token（管理员权限）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| ranking_id | integer | 是 | 榜单ID |

### 请求参数

```json
{
  "rank_start": 1,
  "rank_end": 5,
  "completion_time": "02:30:45",
  "participant_count": 5,
  "team_info": "{\"team_name\":\"闪电队\",\"members\":[\"张三\",\"李四\",\"王五\",\"赵六\",\"钱七\"]}"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| rank_start | integer | 是 | 起始排名 |
| rank_end | integer | 是 | 结束排名 |
| completion_time | string | 是 | 完成时间，格式：HH:MM:SS |
| participant_count | integer | 是 | 参与人数 |
| team_info | string | 否 | 队伍信息JSON字符串 |

### 响应示例

```json
{
  "code": 200,
  "message": "添加榜单明细成功",
  "data": {
    "id": 2,
    "ranking_id": 1,
    "rank_start": 1,
    "rank_end": 5,
    "completion_time": "02:30:45",
    "completion_seconds": 9045,
    "participant_count": 5,
    "team_info": "{\"team_name\":\"闪电队\",\"members\":[\"张三\",\"李四\",\"王五\",\"赵六\",\"钱七\"]}",
    "created_at": "2024-01-15T12:00:00Z",
    "updated_at": "2024-01-15T12:00:00Z"
  }
}
```

## 8. 更新榜单状态

**接口**: `PUT /api/v1/rankings/{ranking_id}/status`
**描述**: 更新榜单状态
**认证**: 需要Bearer Token（管理员权限）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| ranking_id | integer | 是 | 榜单ID |

### 请求参数

```json
{
  "status": "active"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| status | string | 是 | 榜单状态：draft/active/completed/cancelled |

### 响应示例

```json
{
  "code": 200,
  "message": "更新榜单状态成功",
  "data": {
    "id": 1,
    "name": "第1期5人竞速榜",
    "period": 1,
    "ranking_type": "five_person",
    "start_time": "2024-01-15T09:00:00Z",
    "end_time": "2024-01-15T18:00:00Z",
    "team_size_limit": 5,
    "total_participants": 25,
    "status": "active",
    "created_at": "2024-01-14T10:00:00Z",
    "updated_at": "2024-01-15T12:30:00Z"
  }
}
```

---

# 👥 用户管理

## 1. 获取用户列表

**接口**: `GET /api/v1/users`
**描述**: 获取用户列表，支持分页和筛选
**认证**: 需要Bearer Token（管理员权限）

### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| role | string | 否 | - | 用户角色：admin/user |
| is_active | boolean | 否 | - | 是否激活 |
| search | string | 否 | - | 搜索关键词（用户名、昵称） |

### 请求示例

```http
GET /api/v1/users?page=1&size=10&role=user&is_active=true HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": {
    "items": [
      {
        "id": 2,
        "username": "user001",
        "nickname": "普通用户",
        "avatar_url": "https://example.com/avatar2.jpg",
        "email": "<EMAIL>",
        "phone": "13800138001",
        "role": "user",
        "level": "江湖新人",
        "is_active": true,
        "created_at": "2024-01-10T08:00:00Z",
        "last_login_at": "2024-01-15T09:30:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

## 2. 创建用户

**接口**: `POST /api/v1/users`
**描述**: 创建新用户
**认证**: 需要Bearer Token（管理员权限）

### 请求参数

```json
{
  "username": "newuser",
  "password": "password123",
  "nickname": "新用户",
  "email": "<EMAIL>",
  "phone": "13800138002",
  "role": "user"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| username | string | 是 | 用户名，3-50个字符，唯一 |
| password | string | 是 | 密码，最少6个字符 |
| nickname | string | 否 | 昵称，最多100个字符 |
| email | string | 否 | 邮箱地址 |
| phone | string | 否 | 手机号码 |
| role | string | 否 | 用户角色：admin/user，默认user |

### 响应示例

```json
{
  "code": 200,
  "message": "创建用户成功",
  "data": {
    "id": 3,
    "username": "newuser",
    "nickname": "新用户",
    "avatar_url": null,
    "email": "<EMAIL>",
    "phone": "13800138002",
    "role": "user",
    "level": "江湖新人",
    "is_active": true,
    "created_at": "2024-01-15T14:00:00Z",
    "last_login_at": null
  }
}
```

## 3. 获取用户详情

**接口**: `GET /api/v1/users/{user_id}`
**描述**: 获取指定用户的详细信息
**认证**: 需要Bearer Token（管理员权限或本人）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

### 请求示例

```http
GET /api/v1/users/2 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取用户详情成功",
  "data": {
    "id": 2,
    "username": "user001",
    "nickname": "普通用户",
    "avatar_url": "https://example.com/avatar2.jpg",
    "email": "<EMAIL>",
    "phone": "13800138001",
    "bio": "这是一个普通用户",
    "role": "user",
    "level": "江湖新人",
    "is_active": true,
    "created_at": "2024-01-10T08:00:00Z",
    "updated_at": "2024-01-15T09:30:00Z",
    "last_login_at": "2024-01-15T09:30:00Z"
  }
}
```

## 4. 更新用户信息

**接口**: `PUT /api/v1/users/{user_id}`
**描述**: 更新用户信息
**认证**: 需要Bearer Token（管理员权限或本人）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

### 请求参数

```json
{
  "nickname": "更新后的昵称",
  "avatar_url": "https://example.com/new_avatar.jpg",
  "bio": "更新后的个人简介",
  "level": "江湖高手"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| nickname | string | 否 | 昵称，最多100个字符 |
| avatar_url | string | 否 | 头像URL |
| phone | string | 否 | 手机号码 |
| bio | string | 否 | 个人简介 |
| level | string | 否 | 用户等级 |
| is_active | boolean | 否 | 是否激活（仅管理员可修改） |

### 响应示例

```json
{
  "code": 200,
  "message": "更新用户信息成功",
  "data": {
    "id": 2,
    "username": "user001",
    "nickname": "更新后的昵称",
    "avatar_url": "https://example.com/new_avatar.jpg",
    "email": "<EMAIL>",
    "phone": "13800138001",
    "bio": "更新后的个人简介",
    "role": "user",
    "level": "江湖高手",
    "is_active": true,
    "created_at": "2024-01-10T08:00:00Z",
    "updated_at": "2024-01-15T15:00:00Z",
    "last_login_at": "2024-01-15T09:30:00Z"
  }
}
```

## 5. 删除用户

**接口**: `DELETE /api/v1/users/{user_id}`
**描述**: 删除指定用户
**认证**: 需要Bearer Token（管理员权限）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

### 请求示例

```http
DELETE /api/v1/users/3 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "删除用户成功",
  "data": null
}
```

### 注意事项

- 管理员不能删除自己的账户
- 删除用户会同时删除相关的数据记录

---

# 🏢 赞助商管理

## 1. 获取赞助商列表

**接口**: `GET /api/v1/sponsors`
**描述**: 获取赞助商列表，支持分页和筛选
**认证**: 需要Bearer Token

### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| is_active | boolean | 否 | - | 是否启用 |
| is_featured | boolean | 否 | - | 是否推荐 |
| cooperation_level | string | 否 | - | 合作级别 |

### 请求示例

```http
GET /api/v1/sponsors?page=1&size=10&is_active=true&is_featured=true HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取赞助商列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "燕友科技有限公司",
        "logo_url": "https://example.com/logo1.png",
        "avatar_url": "https://example.com/avatar1.png",
        "contact_person": "张经理",
        "contact_phone": "************",
        "contact_email": "<EMAIL>",
        "contact_wechat": "yantech_official",
        "description": "专业的技术服务提供商",
        "website_url": "https://www.yantech.com",
        "address": "北京市朝阳区科技园区",
        "cooperation_level": "金牌合作伙伴",
        "display_order": 1,
        "is_active": true,
        "is_featured": true,
        "cooperation_start_date": "2024-01-01T00:00:00Z",
        "cooperation_end_date": "2024-12-31T23:59:59Z",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-15T14:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

## 2. 创建赞助商

**接口**: `POST /api/v1/sponsors`
**描述**: 创建新赞助商
**认证**: 需要Bearer Token（管理员权限）

### 请求参数

```json
{
  "name": "新赞助商公司",
  "logo_url": "https://example.com/new_logo.png",
  "contact_person": "李经理",
  "contact_phone": "************",
  "contact_email": "<EMAIL>",
  "description": "新的合作伙伴",
  "website_url": "https://www.newsponsor.com",
  "cooperation_level": "银牌合作伙伴",
  "display_order": 2,
  "is_active": true,
  "is_featured": false
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 是 | 赞助商名称，1-200个字符 |
| logo_url | string | 否 | Logo URL |
| avatar_url | string | 否 | 头像URL |
| contact_person | string | 否 | 联系人姓名 |
| contact_phone | string | 否 | 联系电话 |
| contact_email | string | 否 | 联系邮箱 |
| contact_wechat | string | 否 | 微信号 |
| description | string | 否 | 赞助商描述 |
| website_url | string | 否 | 官网地址 |
| address | string | 否 | 地址 |
| cooperation_level | string | 否 | 合作级别 |
| display_order | integer | 否 | 显示顺序，默认0 |
| is_active | boolean | 否 | 是否启用，默认true |
| is_featured | boolean | 否 | 是否推荐，默认false |
| cooperation_start_date | string | 否 | 合作开始日期 |
| cooperation_end_date | string | 否 | 合作结束日期 |

### 响应示例

```json
{
  "code": 200,
  "message": "创建赞助商成功",
  "data": {
    "id": 2,
    "name": "新赞助商公司",
    "logo_url": "https://example.com/new_logo.png",
    "avatar_url": null,
    "contact_person": "李经理",
    "contact_phone": "************",
    "contact_email": "<EMAIL>",
    "contact_wechat": null,
    "description": "新的合作伙伴",
    "website_url": "https://www.newsponsor.com",
    "address": null,
    "cooperation_level": "银牌合作伙伴",
    "display_order": 2,
    "is_active": true,
    "is_featured": false,
    "cooperation_start_date": null,
    "cooperation_end_date": null,
    "created_at": "2024-01-15T16:00:00Z",
    "updated_at": "2024-01-15T16:00:00Z"
  }
}
```

## 3. 获取活跃赞助商列表

**接口**: `GET /api/v1/sponsors/active`
**描述**: 获取活跃赞助商列表（按排序顺序排序）
**认证**: 需要Bearer Token

### 请求示例

```http
GET /api/v1/sponsors/active HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取活跃赞助商列表成功",
  "data": [
    {
      "id": 1,
      "name": "燕友科技有限公司",
      "logo_url": "https://example.com/logo1.png",
      "avatar_url": "https://example.com/avatar1.png",
      "contact_person": "张经理",
      "contact_phone": "************",
      "contact_email": "<EMAIL>",
      "contact_wechat": "yantech_official",
      "description": "专业的技术服务提供商",
      "website_url": "https://www.yantech.com",
      "address": "北京市朝阳区科技园区",
      "cooperation_level": "金牌合作伙伴",
      "display_order": 1,
      "is_active": true,
      "is_featured": true,
      "cooperation_start_date": "2024-01-01T00:00:00Z",
      "cooperation_end_date": "2024-12-31T23:59:59Z",
      "created_at": "2024-01-01T10:00:00Z",
      "updated_at": "2024-01-15T14:00:00Z"
    }
  ]
}
```

## 4. 获取赞助商详情

**接口**: `GET /api/v1/sponsors/{sponsor_id}`
**描述**: 获取指定赞助商的详细信息
**认证**: 需要Bearer Token

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sponsor_id | integer | 是 | 赞助商ID |

### 请求示例

```http
GET /api/v1/sponsors/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取赞助商详情成功",
  "data": {
    "id": 1,
    "name": "燕友科技有限公司",
    "logo_url": "https://example.com/logo1.png",
    "avatar_url": "https://example.com/avatar1.png",
    "contact_person": "张经理",
    "contact_phone": "************",
    "contact_email": "<EMAIL>",
    "contact_wechat": "yantech_official",
    "description": "专业的技术服务提供商，致力于为客户提供优质的解决方案",
    "website_url": "https://www.yantech.com",
    "address": "北京市朝阳区科技园区123号",
    "cooperation_level": "金牌合作伙伴",
    "display_order": 1,
    "is_active": true,
    "is_featured": true,
    "cooperation_start_date": "2024-01-01T00:00:00Z",
    "cooperation_end_date": "2024-12-31T23:59:59Z",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-15T14:00:00Z"
  }
}
```

## 5. 创建赞助商

**接口**: `POST /api/v1/sponsors`
**描述**: 创建新赞助商
**认证**: 需要Bearer Token（管理员权限）

### 请求参数

```json
{
  "name": "新赞助商公司",
  "logo_url": "https://example.com/new_logo.png",
  "contact_person": "李经理",
  "contact_phone": "************",
  "contact_email": "<EMAIL>",
  "description": "新的合作伙伴",
  "website_url": "https://www.newsponsor.com",
  "cooperation_level": "银牌合作伙伴",
  "display_order": 2,
  "is_active": true,
  "is_featured": false
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 是 | 赞助商名称，1-200个字符 |
| logo_url | string | 否 | Logo URL |
| avatar_url | string | 否 | 头像URL |
| contact_person | string | 否 | 联系人姓名 |
| contact_phone | string | 否 | 联系电话 |
| contact_email | string | 否 | 联系邮箱 |
| contact_wechat | string | 否 | 微信号 |
| description | string | 否 | 赞助商描述 |
| website_url | string | 否 | 官网地址 |
| address | string | 否 | 地址 |
| cooperation_level | string | 否 | 合作级别 |
| display_order | integer | 否 | 显示顺序，默认0 |
| is_active | boolean | 否 | 是否启用，默认true |
| is_featured | boolean | 否 | 是否推荐，默认false |
| cooperation_start_date | string | 否 | 合作开始日期 |
| cooperation_end_date | string | 否 | 合作结束日期 |

### 响应示例

```json
{
  "code": 200,
  "message": "创建赞助商成功",
  "data": {
    "id": 2,
    "name": "新赞助商公司",
    "logo_url": "https://example.com/new_logo.png",
    "avatar_url": null,
    "contact_person": "李经理",
    "contact_phone": "************",
    "contact_email": "<EMAIL>",
    "contact_wechat": null,
    "description": "新的合作伙伴",
    "website_url": "https://www.newsponsor.com",
    "address": null,
    "cooperation_level": "银牌合作伙伴",
    "display_order": 2,
    "is_active": true,
    "is_featured": false,
    "cooperation_start_date": null,
    "cooperation_end_date": null,
    "created_at": "2024-01-15T16:00:00Z",
    "updated_at": "2024-01-15T16:00:00Z"
  }
}
```

## 6. 更新赞助商信息

**接口**: `PUT /api/v1/sponsors/{sponsor_id}`
**描述**: 更新赞助商信息
**认证**: 需要Bearer Token（管理员权限）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sponsor_id | integer | 是 | 赞助商ID |

### 请求参数

```json
{
  "name": "更新后的赞助商名称",
  "description": "更新后的描述信息",
  "cooperation_level": "金牌合作伙伴",
  "is_featured": true
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 否 | 赞助商名称 |
| logo_url | string | 否 | Logo URL |
| contact_person | string | 否 | 联系人姓名 |
| contact_phone | string | 否 | 联系电话 |
| contact_email | string | 否 | 联系邮箱 |
| description | string | 否 | 赞助商描述 |
| cooperation_level | string | 否 | 合作级别 |
| is_active | boolean | 否 | 是否启用 |
| is_featured | boolean | 否 | 是否推荐 |

### 响应示例

```json
{
  "code": 200,
  "message": "更新赞助商信息成功",
  "data": {
    "id": 1,
    "name": "更新后的赞助商名称",
    "logo_url": "https://example.com/logo1.png",
    "avatar_url": "https://example.com/avatar1.png",
    "contact_person": "张经理",
    "contact_phone": "************",
    "contact_email": "<EMAIL>",
    "contact_wechat": "yantech_official",
    "description": "更新后的描述信息",
    "website_url": "https://www.yantech.com",
    "address": "北京市朝阳区科技园区123号",
    "cooperation_level": "金牌合作伙伴",
    "display_order": 1,
    "is_active": true,
    "is_featured": true,
    "cooperation_start_date": "2024-01-01T00:00:00Z",
    "cooperation_end_date": "2024-12-31T23:59:59Z",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-15T17:00:00Z"
  }
}
```

## 7. 删除赞助商

**接口**: `DELETE /api/v1/sponsors/{sponsor_id}`
**描述**: 删除指定赞助商
**认证**: 需要Bearer Token（管理员权限）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sponsor_id | integer | 是 | 赞助商ID |

### 请求示例

```http
DELETE /api/v1/sponsors/2 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "删除赞助商成功",
  "data": null
}
```

## 8. 切换赞助商激活状态

**接口**: `PUT /api/v1/sponsors/{sponsor_id}/toggle-status`
**描述**: 切换赞助商的激活状态
**认证**: 需要Bearer Token（管理员权限）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sponsor_id | integer | 是 | 赞助商ID |

### 请求示例

```http
PUT /api/v1/sponsors/1/toggle-status HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "切换赞助商激活状态成功",
  "data": {
    "id": 1,
    "name": "燕友科技有限公司",
    "logo_url": "https://example.com/logo1.png",
    "is_active": false,
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-15T17:30:00Z"
  }
}
```

## 9. 更新赞助商排序

**接口**: `PUT /api/v1/sponsors/{sponsor_id}/sort-order`
**描述**: 更新赞助商排序顺序
**认证**: 需要Bearer Token（管理员权限）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sponsor_id | integer | 是 | 赞助商ID |

### 查询参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sort_order | integer | 是 | 新的排序顺序 |

### 请求示例

```http
PUT /api/v1/sponsors/1/sort-order?sort_order=5 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "更新赞助商排序成功",
  "data": {
    "id": 1,
    "name": "燕友科技有限公司",
    "logo_url": "https://example.com/logo1.png",
    "display_order": 5,
    "is_active": true,
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-15T18:00:00Z"
  }
}
```

---

# � 文件上传管理

## 1. 上传临时Excel文件

**接口**: `POST /api/v1/upload/excel/upload-temp`
**描述**: 上传Excel文件到临时目录，用于后续解析和导入
**认证**: 需要Bearer Token

### 请求参数

- **Content-Type**: `multipart/form-data`
- **file**: Excel文件（.xlsx格式）

### 请求示例

```http
POST /api/v1/upload/excel/upload-temp HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="ranking_data.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

[Excel文件二进制数据]
------WebKitFormBoundary7MA4YWxkTrZu0gW--
```

### 响应示例

```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "filename": "ranking_data.xlsx",
    "temp_file_path": "/tmp/uploads/ranking_data_20240115_123456.xlsx",
    "file_size": 15360,
    "upload_time": "2024-01-15T12:34:56Z"
  }
}
```

## 2. 解析Excel文件

**接口**: `POST /api/v1/upload/excel/parse`
**描述**: 解析榜单明细Excel文件，返回解析后的数据
**认证**: 需要Bearer Token

### 请求参数

- **Content-Type**: `multipart/form-data`
- **file**: 榜单明细Excel文件

### Excel文件格式要求

Excel文件必须包含以下列（按顺序）：
- **排名起始** (rank_start): 整数
- **排名结束** (rank_end): 整数
- **完成时间** (completion_time): 时间格式 HH:MM:SS
- **参与人数** (participant_count): 整数
- **队伍信息** (team_info): 文本（可选）

### 请求示例

```http
POST /api/v1/upload/excel/parse HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="ranking_details.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

[Excel文件二进制数据]
------WebKitFormBoundary7MA4YWxkTrZu0gW--
```

### 响应示例

```json
{
  "code": 200,
  "message": "Excel文件解析成功，共解析5条记录",
  "data": {
    "filename": "ranking_details.xlsx",
    "total_records": 5,
    "ranking_details": [
      {
        "rank_start": 1,
        "rank_end": 5,
        "completion_time": "02:30:45",
        "completion_seconds": 9045,
        "participant_count": 5,
        "team_info": "{\"team_name\":\"闪电队\",\"members\":[\"张三\",\"李四\",\"王五\",\"赵六\",\"钱七\"]}"
      },
      {
        "rank_start": 6,
        "rank_end": 10,
        "completion_time": "02:45:30",
        "completion_seconds": 9930,
        "participant_count": 5,
        "team_info": "{\"team_name\":\"疾风队\",\"members\":[\"小明\",\"小红\",\"小刚\",\"小美\",\"小强\"]}"
      }
    ],
    "parsed_at": "2024-01-15T12:35:00Z"
  }
}
```

## 3. 下载Excel模板

**接口**: `GET /api/v1/upload/excel/template`
**描述**: 下载榜单明细Excel模板文件
**认证**: 需要Bearer Token

### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| ranking_type | string | 否 | 5_person | 榜单类型：5_person/10_person |

### 请求示例

```http
GET /api/v1/upload/excel/template?ranking_type=5_person HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应

返回Excel文件流，文件名格式：`榜单明细模板_{ranking_type}_{YYYYMMDD}.xlsx`

**Content-Type**: `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
**Content-Disposition**: `attachment; filename=榜单明细模板_5_person_20240115.xlsx`

### cURL示例

```bash
curl -X GET "http://localhost:8000/api/v1/upload/excel/template?ranking_type=5_person" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -o "榜单明细模板.xlsx"
```

---

# �🔍 系统健康检查

## 1. 根路径检查

**接口**: `GET /`
**描述**: 系统根路径健康检查
**认证**: 不需要

### 请求示例

```http
GET / HTTP/1.1
Host: localhost:8000
```

### 响应示例

```json
{
  "message": "欢迎使用燕友圈榜单系统",
  "version": "1.0.0",
  "status": "running"
}
```

## 2. 健康检查

**接口**: `GET /health`
**描述**: 系统健康状态检查
**认证**: 不需要

### 请求示例

```http
GET /health HTTP/1.1
Host: localhost:8000
```

### 响应示例

```json
{
  "status": "healthy"
}
```

---

# 📝 附录

## 数据类型说明

### 榜单状态 (RankingStatus)
- `draft`: 草稿
- `active`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消

### 榜单类型 (RankingType)
- `five_person`: 5人榜单
- `ten_person`: 10人榜单

### 用户角色 (UserRole)
- `admin`: 管理员
- `user`: 普通用户

## 时间格式说明

所有时间字段均使用ISO 8601格式：
- 完整格式：`2024-01-15T10:30:00Z`
- 仅时间：`10:30:00`（用于completion_time字段）

## 错误处理

### 参数验证错误示例

```json
{
  "code": 422,
  "message": "参数验证失败",
  "data": {
    "detail": [
      {
        "loc": ["body", "name"],
        "msg": "field required",
        "type": "value_error.missing"
      }
    ]
  }
}
```

### 权限不足错误示例

```json
{
  "code": 403,
  "message": "权限不足",
  "data": null
}
```

### 资源不存在错误示例

```json
{
  "code": 404,
  "message": "资源不存在",
  "data": null
}
```
