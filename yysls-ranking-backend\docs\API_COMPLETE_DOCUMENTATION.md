# 燕友圈榜单系统 - 完整API文档

## 📋 目录

- [基础信息](#基础信息)
- [认证说明](#认证说明)
- [通用响应格式](#通用响应格式)
- [错误码说明](#错误码说明)
- [API端点](#api端点)
  - [认证模块](#认证模块)
  - [榜单管理](#榜单管理)
  - [用户管理](#用户管理)
  - [赞助商管理](#赞助商管理)
  - [系统健康检查](#系统健康检查)

## 🔧 基础信息

- **基础URL**: `http://localhost:8000` (开发环境) / `https://your-domain.com` (生产环境)
- **API版本**: v1
- **API前缀**: `/api/v1`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 🔐 认证说明

### Bearer Token认证

大部分API端点需要Bearer Token认证。在请求头中添加：

```http
Authorization: Bearer <your_access_token>
```

### 获取Token

通过登录接口获取访问令牌：
- 用户名密码登录：`POST /api/v1/auth/login`
- 微信登录：`POST /api/v1/auth/wechat-login`

### Token刷新

Token过期时可通过刷新接口获取新Token：
- `POST /api/v1/auth/refresh`

## 📄 通用响应格式

### 成功响应

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  }
}
```

### 分页响应

```json
{
  "code": 200,
  "message": "获取数据成功",
  "data": {
    "items": [
      // 数据列表
    ],
    "total": 100,
    "page": 1,
    "size": 10,
    "pages": 10
  }
}
```

### 错误响应

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null
}
```

## ❌ 错误码说明

| 错误码 | 说明 | 常见原因 |
|--------|------|----------|
| 200 | 成功 | 请求处理成功 |
| 400 | 请求错误 | 参数格式错误、必填参数缺失 |
| 401 | 未授权 | Token无效、未登录 |
| 403 | 权限不足 | 无操作权限 |
| 404 | 资源不存在 | 请求的资源不存在 |
| 422 | 参数验证失败 | 参数类型错误、格式不正确 |
| 500 | 服务器错误 | 内部服务器错误 |

---

# 🔑 认证模块

## 1. 用户登录

**接口**: `POST /api/v1/auth/login`  
**描述**: 用户名密码登录  
**认证**: 不需要

### 请求参数

```json
{
  "username": "admin",
  "password": "password123"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| username | string | 是 | 用户名，3-50个字符 |
| password | string | 是 | 密码 |

### 响应示例

```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "avatar_url": "https://example.com/avatar.jpg",
      "role": "admin",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 2. 微信登录

**接口**: `POST /api/v1/auth/wechat-login`  
**描述**: 微信授权登录  
**认证**: 不需要

### 请求参数

```json
{
  "code": "wx_auth_code_from_frontend"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| code | string | 是 | 微信授权码 |

### 响应示例

```json
{
  "code": 200,
  "message": "微信登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 2,
      "username": null,
      "nickname": "微信用户",
      "avatar_url": "https://wx.qlogo.cn/mmopen/...",
      "wechat_openid": "oXXXXXXXXXXXXXXXXXXXXXXXXX",
      "role": "user",
      "is_active": true,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

## 3. 获取当前用户信息

**接口**: `GET /api/v1/auth/me`  
**描述**: 获取当前登录用户信息  
**认证**: 需要Bearer Token

### 请求示例

**HTTP请求**：
```http
GET /api/v1/auth/me HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**cURL命令**：
```bash
curl --location --request GET 'http://localhost:8000/api/v1/auth/me' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
--header 'Accept: application/json' \
--header 'Content-Type: application/json' \
--header 'User-Agent: YysRankingSystem/1.0.0' \
--header 'Connection: keep-alive'
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取用户信息成功",
  "data": {
    "id": 1,
    "username": "admin",
    "nickname": "管理员",
    "avatar_url": "https://example.com/avatar.jpg",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "role": "admin",
    "level": "管理员",
    "bio": "系统管理员",
    "is_active": true,
    "created_at": "2024-01-01T00:00:00Z",
    "last_login_at": "2024-01-15T10:30:00Z"
  }
}
```

## 4. 刷新Token

**接口**: `POST /api/v1/auth/refresh`  
**描述**: 刷新访问令牌  
**认证**: 需要Bearer Token

### 请求示例

```http
POST /api/v1/auth/refresh HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 1,
      "username": "admin",
      "nickname": "管理员",
      "role": "admin"
    }
  }
}
```

## 5. 用户登出

**接口**: `POST /api/v1/auth/logout`  
**描述**: 用户登出，使Token失效  
**认证**: 需要Bearer Token

### 请求示例

```http
POST /api/v1/auth/logout HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

---

# 📊 榜单管理

## 1. 获取榜单列表

**接口**: `GET /api/v1/rankings`  
**描述**: 获取榜单列表，支持分页和筛选  
**认证**: 需要Bearer Token

### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| status | string | 否 | - | 榜单状态：draft/active/completed/cancelled |
| ranking_type | string | 否 | - | 榜单类型：five_person/ten_person |

### 请求示例

```http
GET /api/v1/rankings?page=1&size=10&status=active&ranking_type=five_person HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取榜单列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "第1期5人竞速榜",
        "period": 1,
        "ranking_type": "five_person",
        "start_time": "2024-01-15T09:00:00Z",
        "end_time": "2024-01-15T18:00:00Z",
        "team_size_limit": 5,
        "total_participants": 25,
        "status": "active",
        "created_at": "2024-01-14T10:00:00Z",
        "updated_at": "2024-01-15T08:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

## 2. 创建榜单

**接口**: `POST /api/v1/rankings`
**描述**: 创建新榜单
**认证**: 需要Bearer Token（管理员权限）

### 请求参数

```json
{
  "name": "第2期10人竞速榜",
  "period": 2,
  "ranking_type": "ten_person",
  "start_time": "2024-01-20T09:00:00Z",
  "end_time": "2024-01-20T18:00:00Z",
  "team_size_limit": 10
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 是 | 榜单名称，1-200个字符 |
| period | integer | 是 | 期数，必须大于0 |
| ranking_type | string | 是 | 榜单类型：five_person/ten_person |
| start_time | string | 是 | 开始时间，ISO 8601格式 |
| end_time | string | 是 | 结束时间，ISO 8601格式 |
| team_size_limit | integer | 是 | 组队人数限制，1-20人 |

### 响应示例

```json
{
  "code": 200,
  "message": "创建榜单成功",
  "data": {
    "id": 2,
    "name": "第2期10人竞速榜",
    "period": 2,
    "ranking_type": "ten_person",
    "start_time": "2024-01-20T09:00:00Z",
    "end_time": "2024-01-20T18:00:00Z",
    "team_size_limit": 10,
    "total_participants": 0,
    "status": "draft",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

## 3. 获取榜单详情

**接口**: `GET /api/v1/rankings/{ranking_id}`
**描述**: 获取指定榜单的详细信息
**认证**: 需要Bearer Token

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| ranking_id | integer | 是 | 榜单ID |

### 请求示例

```http
GET /api/v1/rankings/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取榜单详情成功",
  "data": {
    "id": 1,
    "name": "第1期5人竞速榜",
    "period": 1,
    "ranking_type": "five_person",
    "start_time": "2024-01-15T09:00:00Z",
    "end_time": "2024-01-15T18:00:00Z",
    "team_size_limit": 5,
    "total_participants": 25,
    "status": "active",
    "created_at": "2024-01-14T10:00:00Z",
    "updated_at": "2024-01-15T08:00:00Z",
    "details": [
      {
        "id": 1,
        "ranking_id": 1,
        "rank_start": 1,
        "rank_end": 5,
        "completion_time": "02:30:45",
        "completion_seconds": 9045,
        "participant_count": 5,
        "team_info": "{\"team_name\":\"闪电队\",\"members\":[\"张三\",\"李四\",\"王五\",\"赵六\",\"钱七\"]}",
        "created_at": "2024-01-15T11:30:45Z",
        "updated_at": "2024-01-15T11:30:45Z"
      }
    ]
  }
}
```

## 4. 更新榜单

**接口**: `PUT /api/v1/rankings/{ranking_id}`
**描述**: 更新榜单信息
**认证**: 需要Bearer Token（管理员权限）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| ranking_id | integer | 是 | 榜单ID |

### 请求参数

```json
{
  "name": "第1期5人竞速榜（更新）",
  "start_time": "2024-01-15T10:00:00Z",
  "end_time": "2024-01-15T19:00:00Z",
  "status": "active"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 否 | 榜单名称，1-200个字符 |
| start_time | string | 否 | 开始时间，ISO 8601格式 |
| end_time | string | 否 | 结束时间，ISO 8601格式 |
| status | string | 否 | 榜单状态：draft/active/completed/cancelled |

### 响应示例

```json
{
  "code": 200,
  "message": "更新榜单成功",
  "data": {
    "id": 1,
    "name": "第1期5人竞速榜（更新）",
    "period": 1,
    "ranking_type": "five_person",
    "start_time": "2024-01-15T10:00:00Z",
    "end_time": "2024-01-15T19:00:00Z",
    "team_size_limit": 5,
    "total_participants": 25,
    "status": "active",
    "created_at": "2024-01-14T10:00:00Z",
    "updated_at": "2024-01-15T12:00:00Z"
  }
}
```

## 5. 删除榜单

**接口**: `DELETE /api/v1/rankings/{ranking_id}`
**描述**: 删除指定榜单
**认证**: 需要Bearer Token（管理员权限）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| ranking_id | integer | 是 | 榜单ID |

### 请求示例

```http
DELETE /api/v1/rankings/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "删除榜单成功",
  "data": null
}
```

---

# 👥 用户管理

## 1. 获取用户列表

**接口**: `GET /api/v1/users`
**描述**: 获取用户列表，支持分页和筛选
**认证**: 需要Bearer Token（管理员权限）

### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| role | string | 否 | - | 用户角色：admin/user |
| is_active | boolean | 否 | - | 是否激活 |
| search | string | 否 | - | 搜索关键词（用户名、昵称） |

### 请求示例

```http
GET /api/v1/users?page=1&size=10&role=user&is_active=true HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取用户列表成功",
  "data": {
    "items": [
      {
        "id": 2,
        "username": "user001",
        "nickname": "普通用户",
        "avatar_url": "https://example.com/avatar2.jpg",
        "email": "<EMAIL>",
        "phone": "13800138001",
        "role": "user",
        "level": "江湖新人",
        "is_active": true,
        "created_at": "2024-01-10T08:00:00Z",
        "last_login_at": "2024-01-15T09:30:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

## 2. 创建用户

**接口**: `POST /api/v1/users`
**描述**: 创建新用户
**认证**: 需要Bearer Token（管理员权限）

### 请求参数

```json
{
  "username": "newuser",
  "password": "password123",
  "nickname": "新用户",
  "email": "<EMAIL>",
  "phone": "13800138002",
  "role": "user"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| username | string | 是 | 用户名，3-50个字符，唯一 |
| password | string | 是 | 密码，最少6个字符 |
| nickname | string | 否 | 昵称，最多100个字符 |
| email | string | 否 | 邮箱地址 |
| phone | string | 否 | 手机号码 |
| role | string | 否 | 用户角色：admin/user，默认user |

### 响应示例

```json
{
  "code": 200,
  "message": "创建用户成功",
  "data": {
    "id": 3,
    "username": "newuser",
    "nickname": "新用户",
    "avatar_url": null,
    "email": "<EMAIL>",
    "phone": "13800138002",
    "role": "user",
    "level": "江湖新人",
    "is_active": true,
    "created_at": "2024-01-15T14:00:00Z",
    "last_login_at": null
  }
}
```

## 3. 获取用户详情

**接口**: `GET /api/v1/users/{user_id}`
**描述**: 获取指定用户的详细信息
**认证**: 需要Bearer Token（管理员权限或本人）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

### 请求示例

```http
GET /api/v1/users/2 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取用户详情成功",
  "data": {
    "id": 2,
    "username": "user001",
    "nickname": "普通用户",
    "avatar_url": "https://example.com/avatar2.jpg",
    "email": "<EMAIL>",
    "phone": "13800138001",
    "bio": "这是一个普通用户",
    "role": "user",
    "level": "江湖新人",
    "is_active": true,
    "created_at": "2024-01-10T08:00:00Z",
    "updated_at": "2024-01-15T09:30:00Z",
    "last_login_at": "2024-01-15T09:30:00Z"
  }
}
```

## 4. 更新用户信息

**接口**: `PUT /api/v1/users/{user_id}`
**描述**: 更新用户信息
**认证**: 需要Bearer Token（管理员权限或本人）

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| user_id | integer | 是 | 用户ID |

### 请求参数

```json
{
  "nickname": "更新后的昵称",
  "avatar_url": "https://example.com/new_avatar.jpg",
  "bio": "更新后的个人简介",
  "level": "江湖高手"
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| nickname | string | 否 | 昵称，最多100个字符 |
| avatar_url | string | 否 | 头像URL |
| phone | string | 否 | 手机号码 |
| bio | string | 否 | 个人简介 |
| level | string | 否 | 用户等级 |
| is_active | boolean | 否 | 是否激活（仅管理员可修改） |

### 响应示例

```json
{
  "code": 200,
  "message": "更新用户信息成功",
  "data": {
    "id": 2,
    "username": "user001",
    "nickname": "更新后的昵称",
    "avatar_url": "https://example.com/new_avatar.jpg",
    "email": "<EMAIL>",
    "phone": "13800138001",
    "bio": "更新后的个人简介",
    "role": "user",
    "level": "江湖高手",
    "is_active": true,
    "created_at": "2024-01-10T08:00:00Z",
    "updated_at": "2024-01-15T15:00:00Z",
    "last_login_at": "2024-01-15T09:30:00Z"
  }
}
```

---

# 🏢 赞助商管理

## 1. 获取赞助商列表

**接口**: `GET /api/v1/sponsors`
**描述**: 获取赞助商列表，支持分页和筛选
**认证**: 需要Bearer Token

### 查询参数

| 参数 | 类型 | 必填 | 默认值 | 描述 |
|------|------|------|--------|------|
| page | integer | 否 | 1 | 页码，从1开始 |
| size | integer | 否 | 10 | 每页大小，最大100 |
| is_active | boolean | 否 | - | 是否启用 |
| is_featured | boolean | 否 | - | 是否推荐 |
| cooperation_level | string | 否 | - | 合作级别 |

### 请求示例

```http
GET /api/v1/sponsors?page=1&size=10&is_active=true&is_featured=true HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取赞助商列表成功",
  "data": {
    "items": [
      {
        "id": 1,
        "name": "燕友科技有限公司",
        "logo_url": "https://example.com/logo1.png",
        "avatar_url": "https://example.com/avatar1.png",
        "contact_person": "张经理",
        "contact_phone": "************",
        "contact_email": "<EMAIL>",
        "contact_wechat": "yantech_official",
        "description": "专业的技术服务提供商",
        "website_url": "https://www.yantech.com",
        "address": "北京市朝阳区科技园区",
        "cooperation_level": "金牌合作伙伴",
        "display_order": 1,
        "is_active": true,
        "is_featured": true,
        "cooperation_start_date": "2024-01-01T00:00:00Z",
        "cooperation_end_date": "2024-12-31T23:59:59Z",
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-15T14:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "size": 10,
    "pages": 1
  }
}
```

## 2. 创建赞助商

**接口**: `POST /api/v1/sponsors`
**描述**: 创建新赞助商
**认证**: 需要Bearer Token（管理员权限）

### 请求参数

```json
{
  "name": "新赞助商公司",
  "logo_url": "https://example.com/new_logo.png",
  "contact_person": "李经理",
  "contact_phone": "************",
  "contact_email": "<EMAIL>",
  "description": "新的合作伙伴",
  "website_url": "https://www.newsponsor.com",
  "cooperation_level": "银牌合作伙伴",
  "display_order": 2,
  "is_active": true,
  "is_featured": false
}
```

| 字段 | 类型 | 必填 | 描述 |
|------|------|------|------|
| name | string | 是 | 赞助商名称，1-200个字符 |
| logo_url | string | 否 | Logo URL |
| avatar_url | string | 否 | 头像URL |
| contact_person | string | 否 | 联系人姓名 |
| contact_phone | string | 否 | 联系电话 |
| contact_email | string | 否 | 联系邮箱 |
| contact_wechat | string | 否 | 微信号 |
| description | string | 否 | 赞助商描述 |
| website_url | string | 否 | 官网地址 |
| address | string | 否 | 地址 |
| cooperation_level | string | 否 | 合作级别 |
| display_order | integer | 否 | 显示顺序，默认0 |
| is_active | boolean | 否 | 是否启用，默认true |
| is_featured | boolean | 否 | 是否推荐，默认false |
| cooperation_start_date | string | 否 | 合作开始日期 |
| cooperation_end_date | string | 否 | 合作结束日期 |

### 响应示例

```json
{
  "code": 200,
  "message": "创建赞助商成功",
  "data": {
    "id": 2,
    "name": "新赞助商公司",
    "logo_url": "https://example.com/new_logo.png",
    "avatar_url": null,
    "contact_person": "李经理",
    "contact_phone": "************",
    "contact_email": "<EMAIL>",
    "contact_wechat": null,
    "description": "新的合作伙伴",
    "website_url": "https://www.newsponsor.com",
    "address": null,
    "cooperation_level": "银牌合作伙伴",
    "display_order": 2,
    "is_active": true,
    "is_featured": false,
    "cooperation_start_date": null,
    "cooperation_end_date": null,
    "created_at": "2024-01-15T16:00:00Z",
    "updated_at": "2024-01-15T16:00:00Z"
  }
}
```

## 3. 获取赞助商详情

**接口**: `GET /api/v1/sponsors/{sponsor_id}`
**描述**: 获取指定赞助商的详细信息
**认证**: 需要Bearer Token

### 路径参数

| 参数 | 类型 | 必填 | 描述 |
|------|------|------|------|
| sponsor_id | integer | 是 | 赞助商ID |

### 请求示例

```http
GET /api/v1/sponsors/1 HTTP/1.1
Host: localhost:8000
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 响应示例

```json
{
  "code": 200,
  "message": "获取赞助商详情成功",
  "data": {
    "id": 1,
    "name": "燕友科技有限公司",
    "logo_url": "https://example.com/logo1.png",
    "avatar_url": "https://example.com/avatar1.png",
    "contact_person": "张经理",
    "contact_phone": "************",
    "contact_email": "<EMAIL>",
    "contact_wechat": "yantech_official",
    "description": "专业的技术服务提供商，致力于为客户提供优质的解决方案",
    "website_url": "https://www.yantech.com",
    "address": "北京市朝阳区科技园区123号",
    "cooperation_level": "金牌合作伙伴",
    "display_order": 1,
    "is_active": true,
    "is_featured": true,
    "cooperation_start_date": "2024-01-01T00:00:00Z",
    "cooperation_end_date": "2024-12-31T23:59:59Z",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-15T14:00:00Z"
  }
}
```

---

# 🔍 系统健康检查

## 1. 根路径检查

**接口**: `GET /`
**描述**: 系统根路径健康检查
**认证**: 不需要

### 请求示例

```http
GET / HTTP/1.1
Host: localhost:8000
```

### 响应示例

```json
{
  "message": "欢迎使用燕友圈榜单系统",
  "version": "1.0.0",
  "status": "running"
}
```

## 2. 健康检查

**接口**: `GET /health`
**描述**: 系统健康状态检查
**认证**: 不需要

### 请求示例

```http
GET /health HTTP/1.1
Host: localhost:8000
```

### 响应示例

```json
{
  "status": "healthy"
}
```

---

# 📝 附录

## 数据类型说明

### 榜单状态 (RankingStatus)
- `draft`: 草稿
- `active`: 进行中
- `completed`: 已完成
- `cancelled`: 已取消

### 榜单类型 (RankingType)
- `five_person`: 5人榜单
- `ten_person`: 10人榜单

### 用户角色 (UserRole)
- `admin`: 管理员
- `user`: 普通用户

## 时间格式说明

所有时间字段均使用ISO 8601格式：
- 完整格式：`2024-01-15T10:30:00Z`
- 仅时间：`10:30:00`（用于completion_time字段）

## 错误处理

### 参数验证错误示例

```json
{
  "code": 422,
  "message": "参数验证失败",
  "data": {
    "detail": [
      {
        "loc": ["body", "name"],
        "msg": "field required",
        "type": "value_error.missing"
      }
    ]
  }
}
```

### 权限不足错误示例

```json
{
  "code": 403,
  "message": "权限不足",
  "data": null
}
```

### 资源不存在错误示例

```json
{
  "code": 404,
  "message": "资源不存在",
  "data": null
}
```
