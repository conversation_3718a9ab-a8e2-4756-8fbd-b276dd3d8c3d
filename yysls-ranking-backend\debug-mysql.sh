#!/bin/bash

# MySQL 启动问题调试脚本

echo "🔍 MySQL 启动问题调试"
echo "===================="

# 1. 检查 Docker Compose 配置
echo "1. 检查 Docker Compose 配置..."
if docker-compose -f docker-compose.prod.yml config >/dev/null 2>&1; then
    echo "✅ Docker Compose 配置语法正确"
else
    echo "❌ Docker Compose 配置有语法错误："
    docker-compose -f docker-compose.prod.yml config
    exit 1
fi

# 2. 检查环境变量
echo ""
echo "2. 检查环境变量加载..."
echo "MySQL 相关环境变量："
docker-compose -f docker-compose.prod.yml config | grep -E "MYSQL_|DATABASE_" | head -10

# 3. 停止现有容器
echo ""
echo "3. 清理现有容器..."
docker-compose -f docker-compose.prod.yml down
docker rm -f yysls-ranking-db 2>/dev/null || true

# 4. 创建测试用的最小配置
echo ""
echo "4. 创建测试配置..."
cat > mysql-test.cnf << 'EOF'
[mysqld]
default-authentication-plugin=mysql_native_password
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
log_error=stderr
skip-log-bin
performance_schema=OFF
EOF

# 5. 使用最小配置启动测试容器
echo ""
echo "5. 启动测试容器..."
docker run -d \
    --name mysql-debug-test \
    --env-file .env.prod \
    -v $(pwd)/mysql-test.cnf:/etc/mysql/conf.d/test.cnf:ro \
    -p 3307:3306 \
    mysql:8.0

echo "等待容器启动..."
sleep 5

# 6. 检查容器状态
echo ""
echo "6. 检查容器状态..."
if docker ps | grep -q mysql-debug-test; then
    echo "✅ 测试容器启动成功"
    
    # 等待 MySQL 服务就绪
    echo "等待 MySQL 服务就绪..."
    for i in {1..30}; do
        if docker logs mysql-debug-test 2>&1 | grep -q "ready for connections"; then
            echo "✅ MySQL 服务就绪"
            break
        fi
        echo "等待中... ($i/30)"
        sleep 2
    done
    
    # 显示成功信息
    echo ""
    echo "🎉 MySQL 可以正常启动！"
    echo "问题可能在于："
    echo "1. Docker Compose 中的复杂配置"
    echo "2. 卷挂载问题"
    echo "3. 网络配置问题"
    
else
    echo "❌ 测试容器启动失败"
    echo ""
    echo "容器日志："
    docker logs mysql-debug-test
    
    echo ""
    echo "可能的问题："
    echo "1. 环境变量配置错误"
    echo "2. MySQL 镜像问题"
    echo "3. 系统兼容性问题"
fi

# 7. 如果成功，测试原始配置
if docker ps | grep -q mysql-debug-test; then
    echo ""
    echo "7. 测试原始 MySQL 配置..."
    
    # 停止测试容器
    docker stop mysql-debug-test
    docker rm mysql-debug-test
    
    # 使用原始配置启动
    docker run -d \
        --name mysql-debug-original \
        --env-file .env.prod \
        -v $(pwd)/mysql/conf.d:/etc/mysql/conf.d:ro \
        -p 3307:3306 \
        mysql:8.0
    
    echo "等待原始配置容器启动..."
    sleep 10
    
    if docker ps | grep -q mysql-debug-original; then
        echo "✅ 原始配置也能正常启动"
        echo "问题可能在 Docker Compose 的其他配置"
    else
        echo "❌ 原始配置启动失败"
        echo "问题在于 MySQL 配置文件"
        echo ""
        echo "原始配置容器日志："
        docker logs mysql-debug-original
    fi
    
    # 清理
    docker stop mysql-debug-original 2>/dev/null || true
    docker rm mysql-debug-original 2>/dev/null || true
fi

# 8. 清理测试资源
echo ""
echo "8. 清理测试资源..."
docker stop mysql-debug-test 2>/dev/null || true
docker rm mysql-debug-test 2>/dev/null || true
rm -f mysql-test.cnf

# 9. 提供解决建议
echo ""
echo "🔧 解决建议："
echo "============"
echo ""
echo "如果测试容器能启动："
echo "1. 简化 Docker Compose 中的 MySQL 配置"
echo "2. 检查卷挂载路径是否正确"
echo "3. 尝试不挂载配置文件启动"
echo ""
echo "如果测试容器不能启动："
echo "1. 检查 .env.prod 中的 MySQL 环境变量"
echo "2. 尝试使用 MySQL 5.7 版本"
echo "3. 检查系统 Docker 版本兼容性"
echo ""
echo "立即尝试的命令："
echo "# 不使用自定义配置启动"
echo "docker-compose -f docker-compose.prod.yml up db --no-deps"
echo ""
echo "# 或者临时移除配置文件"
echo "mv mysql/conf.d/mysql.cnf mysql/conf.d/mysql.cnf.bak"
echo "docker-compose -f docker-compose.prod.yml up -d db"

echo ""
echo "✅ 调试完成"
