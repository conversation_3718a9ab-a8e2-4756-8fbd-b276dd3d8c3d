# 燕友圈榜单系统 - 生产环境安全配置
# 使用外部卷管理敏感数据

version: '3.8'

services:
  # 应用服务
  app:
    build: .
    container_name: yysls-ranking-app
    restart: unless-stopped
    expose:
      - "8000"
    env_file:
      - .env.prod
    environment:
      - DATABASE_URL=mysql+pymysql://root:${MYSQL_ROOT_PASSWORD}@db:3306/${MYSQL_DATABASE}?charset=utf8mb4
      - DATABASE_URL_ASYNC=mysql+aiomysql://root:${MYSQL_ROOT_PASSWORD}@db:3306/${MYSQL_DATABASE}?charset=utf8mb4
      - REDIS_URL=redis://redis:6379/0
      - TZ=Asia/Shanghai
    depends_on:
      - db
      - redis
    volumes:
      - app_logs:/app/logs
      - app_uploads:/app/uploads
    networks:
      - yysls-network
    # 安全配置
    read_only: false
    tmpfs:
      - /tmp
    user: "1000:1000"  # 非root用户运行

  # 数据库服务
  db:
    image: mysql:8.0
    container_name: yysls-ranking-db
    restart: unless-stopped
    env_file:
      - .env.prod
    environment:
      - MYSQL_DATABASE=${MYSQL_DATABASE}
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - TZ=Asia/Shanghai
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=256M
      --max-connections=200
      --bind-address=0.0.0.0
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/conf.d:/etc/mysql/conf.d:ro
    # 不暴露端口到主机（仅内部访问）
    expose:
      - "3306"
    networks:
      - yysls-network
    # 安全配置
    user: "999:999"  # mysql用户

  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: yysls-ranking-redis
    restart: unless-stopped
    command: >
      redis-server
      --appendonly yes
      --requirepass ${REDIS_PASSWORD:-defaultpassword}
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    expose:
      - "6379"
    networks:
      - yysls-network
    environment:
      - TZ=Asia/Shanghai

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: yysls-ranking-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      # 使用外部卷管理SSL证书（更安全）
      - ssl_certs:/etc/nginx/ssl:ro
      - app_uploads:/app/uploads:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - yysls-network
    environment:
      - TZ=Asia/Shanghai
    # 安全配置
    read_only: true
    tmpfs:
      - /var/cache/nginx
      - /var/run

# 使用外部卷管理敏感数据
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local
  # SSL证书外部卷（需要手动创建和管理）
  ssl_certs:
    external: true
    name: yysls_ssl_certs

networks:
  yysls-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
