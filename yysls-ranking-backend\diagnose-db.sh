#!/bin/bash

# 燕友圈榜单系统 - 数据库问题诊断脚本

set -e

echo "🔍 数据库问题诊断工具"
echo "===================="

# 1. 检查容器状态
echo "1. 检查容器状态..."
echo "容器列表："
docker ps -a | grep -E "(CONTAINER|yysls.*db|mysql)"

echo ""
echo "数据库容器详细信息："
if docker ps -a | grep -q yysls-ranking-db; then
    docker inspect yysls-ranking-db | jq '.[0].State'
else
    echo "❌ 数据库容器不存在"
fi

# 2. 检查环境变量
echo ""
echo "2. 检查环境变量..."
if [ -f ".env.prod" ]; then
    echo "环境变量文件存在："
    echo "MYSQL_ROOT_PASSWORD: $(grep MYSQL_ROOT_PASSWORD .env.prod | cut -d'=' -f2 | sed 's/./*/g')"
    echo "MYSQL_DATABASE: $(grep MYSQL_DATABASE .env.prod | cut -d'=' -f2)"
    echo "MYSQL_USER: $(grep MYSQL_USER .env.prod | cut -d'=' -f2)"
    echo "MYSQL_PASSWORD: $(grep MYSQL_PASSWORD .env.prod | cut -d'=' -f2 | sed 's/./*/g')"
else
    echo "❌ .env.prod 文件不存在"
fi

# 3. 检查数据库日志
echo ""
echo "3. 检查数据库日志（最近50行）..."
if docker ps -a | grep -q yysls-ranking-db; then
    echo "--- 数据库日志开始 ---"
    docker logs yysls-ranking-db --tail=50
    echo "--- 数据库日志结束 ---"
else
    echo "❌ 无法获取数据库日志，容器不存在"
fi

# 4. 检查资源使用
echo ""
echo "4. 检查系统资源..."
echo "内存使用情况："
free -h

echo ""
echo "磁盘使用情况："
df -h

echo ""
echo "Docker 资源使用："
if docker ps | grep -q yysls-ranking-db; then
    docker stats yysls-ranking-db --no-stream
else
    echo "数据库容器未运行"
fi

# 5. 检查数据卷
echo ""
echo "5. 检查数据卷..."
echo "Docker 卷列表："
docker volume ls | grep -E "(DRIVER|mysql|yysls)"

if docker volume ls | grep -q mysql_data; then
    echo ""
    echo "MySQL 数据卷信息："
    docker volume inspect yysls-ranking-backend_mysql_data 2>/dev/null || \
    docker volume inspect mysql_data 2>/dev/null || \
    echo "未找到 MySQL 数据卷"
fi

# 6. 网络检查
echo ""
echo "6. 检查网络连接..."
echo "Docker 网络："
docker network ls | grep -E "(NETWORK|yysls)"

if docker network ls | grep -q yysls-network; then
    echo ""
    echo "网络详细信息："
    docker network inspect yysls-network | jq '.[0].Containers' 2>/dev/null || \
    docker network inspect yysls-network
fi

# 7. 提供解决建议
echo ""
echo "🔧 常见问题解决方案："
echo "==================="

echo ""
echo "问题1: 环境变量未设置"
echo "解决: 检查 .env.prod 文件是否包含所有必需的 MySQL 环境变量"

echo ""
echo "问题2: 内存不足"
echo "解决: 确保系统有足够内存（建议至少2GB可用）"

echo ""
echo "问题3: 数据卷权限问题"
echo "解决: 重新创建数据卷"
echo "   docker-compose down -v"
echo "   docker volume prune"
echo "   docker-compose up -d"

echo ""
echo "问题4: 配置文件错误"
echo "解决: 检查 MySQL 配置文件"
echo "   ls -la mysql/conf.d/"

echo ""
echo "问题5: 端口冲突"
echo "解决: 检查端口3306是否被占用"
echo "   netstat -tlnp | grep 3306"
echo "   lsof -i :3306"

# 8. 自动修复选项
echo ""
echo "🚀 自动修复选项："
echo "================"
echo "1. 重启数据库服务"
echo "2. 重新创建数据库容器"
echo "3. 清理并重建所有服务"
echo "4. 查看实时日志"
echo "5. 退出"

read -p "请选择修复选项 (1-5): " fix_option

case $fix_option in
    1)
        echo "重启数据库服务..."
        docker-compose restart db
        echo "等待服务启动..."
        sleep 10
        docker logs yysls-ranking-db --tail=20
        ;;
    2)
        echo "重新创建数据库容器..."
        docker-compose stop db
        docker-compose rm -f db
        docker-compose up -d db
        echo "等待服务启动..."
        sleep 15
        docker logs yysls-ranking-db --tail=20
        ;;
    3)
        echo "清理并重建所有服务..."
        docker-compose down -v
        docker-compose up -d
        echo "等待服务启动..."
        sleep 20
        docker-compose ps
        ;;
    4)
        echo "查看实时日志（按 Ctrl+C 退出）..."
        docker logs yysls-ranking-db -f
        ;;
    5)
        echo "退出诊断工具"
        ;;
    *)
        echo "无效选择"
        ;;
esac

echo ""
echo "✅ 诊断完成"
