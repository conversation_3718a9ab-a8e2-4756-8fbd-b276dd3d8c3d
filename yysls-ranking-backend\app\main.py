"""
FastAPI应用主入口
"""
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from app.config import settings
from app.api.v1.api import api_router


def create_application() -> FastAPI:
    """创建FastAPI应用实例"""
    
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        description="燕友圈榜单系统后端API服务",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
    )
    
    # 添加CORS中间件
    if settings.allowed_origins:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=settings.allowed_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # 添加可信主机中间件
    app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])
    
    # 注册路由
    app.include_router(api_router, prefix="/api/v1")
    
    @app.get("/")
    async def root():
        """根路径健康检查"""
        return {
            "message": f"欢迎使用{settings.app_name}",
            "version": settings.app_version,
            "status": "running"
        }
    
    @app.get("/health")
    async def health_check():
        """健康检查端点"""
        return {"status": "healthy"}
    
    return app


# 创建应用实例
app = create_application()
