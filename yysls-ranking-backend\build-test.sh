#!/bin/bash

# 燕友圈榜单系统 - Docker 构建测试脚本

set -e

echo "🐳 Docker 构建测试 - 使用国内源"
echo "================================"

# 显示当前配置
echo "1. 当前pip配置："
if [ -f "pip.conf" ]; then
    echo "✅ pip.conf 文件存在"
    echo "   主要源: $(grep 'index-url' pip.conf | cut -d'=' -f2 | xargs)"
else
    echo "❌ pip.conf 文件不存在"
fi

# 构建镜像
echo ""
echo "2. 开始构建Docker镜像..."
echo "   使用国内源加速包安装"

# 记录开始时间
start_time=$(date +%s)

# 构建镜像
docker build -t yysls-ranking:test . --no-cache

# 记录结束时间
end_time=$(date +%s)
duration=$((end_time - start_time))

echo ""
echo "✅ 构建完成！"
echo "   耗时: ${duration} 秒"

# 显示镜像信息
echo ""
echo "3. 镜像信息："
docker images yysls-ranking:test

# 测试运行
echo ""
echo "4. 测试运行容器..."
container_id=$(docker run -d -p 8001:8000 --name yysls-test yysls-ranking:test)

echo "   容器ID: $container_id"
echo "   等待服务启动..."
sleep 5

# 检查容器状态
if docker ps | grep -q yysls-test; then
    echo "✅ 容器运行正常"
    echo "   测试地址: http://localhost:8001"
    
    # 简单的健康检查
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        echo "✅ 健康检查通过"
    else
        echo "⚠️  健康检查失败，查看日志："
        docker logs yysls-test --tail=10
    fi
else
    echo "❌ 容器启动失败"
    docker logs yysls-test
fi

# 清理
echo ""
echo "5. 清理测试容器..."
docker stop yysls-test > /dev/null 2>&1 || true
docker rm yysls-test > /dev/null 2>&1 || true

echo ""
echo "🎉 测试完成！"
echo ""
echo "💡 使用说明："
echo "   - 如果测试成功，可以使用: docker tag yysls-ranking:test yysls-ranking:latest"
echo "   - 生产部署: docker-compose -f docker-compose.prod.yml up --build -d"
echo ""
echo "📊 构建优化效果："
echo "   - 使用国内pip源，大幅提升包安装速度"
echo "   - 多个备用源确保稳定性"
echo "   - 构建时间: ${duration} 秒"
