"""
基础服务类

提供所有业务服务的基础功能和通用方法
"""
from typing import Any, Dict, List, Optional, Type, TypeVar, Generic
from sqlalchemy.orm import Session
from sqlalchemy import select, update, delete, and_, or_, func
from pydantic import BaseModel

from app.core.database import Base
from app.core.logger import get_logger

# 泛型类型变量
ModelType = TypeVar("ModelType", bound=Base)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class BaseService(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    """基础服务类"""
    
    def __init__(self, model: Type[ModelType]):
        """
        初始化服务

        Args:
            model: SQLAlchemy模型类
        """
        self.model = model
        self.logger = get_logger(self.__class__.__name__)
    
    def get(self, db: Session, id: Any) -> Optional[ModelType]:
        """根据ID获取单个记录"""
        try:
            result = db.execute(select(self.model).where(self.model.id == id))
            return result.scalar_one_or_none()
        except Exception as e:
            self.logger.error(f"获取记录失败 ID={id}: {str(e)}")
            raise
    
    def get_multi(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None
    ) -> List[ModelType]:
        """获取多个记录"""
        try:
            query = select(self.model)

            # 应用过滤条件
            if filters:
                for key, value in filters.items():
                    if hasattr(self.model, key) and value is not None:
                        query = query.where(getattr(self.model, key) == value)

            # 应用排序
            if order_by and hasattr(self.model, order_by):
                query = query.order_by(getattr(self.model, order_by))

            # 应用分页
            query = query.offset(skip).limit(limit)

            result = db.execute(query)
            return result.scalars().all()
        except Exception as e:
            self.logger.error(f"获取记录列表失败: {str(e)}")
            raise
    
    def create(self, db: Session, obj_in: CreateSchemaType) -> ModelType:
        """创建新记录"""
        try:
            obj_data = obj_in.dict() if hasattr(obj_in, 'dict') else obj_in
            db_obj = self.model(**obj_data)
            db.add(db_obj)
            db.commit()
            db.refresh(db_obj)

            self.logger.info(f"创建记录成功 ID={db_obj.id}")
            return db_obj
        except Exception as e:
            db.rollback()
            self.logger.error(f"创建记录失败: {str(e)}")
            raise
    
    def update(
        self,
        db: Session,
        db_obj: ModelType,
        obj_in: UpdateSchemaType
    ) -> ModelType:
        """更新记录"""
        try:
            obj_data = obj_in.dict(exclude_unset=True) if hasattr(obj_in, 'dict') else obj_in

            for field, value in obj_data.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)

            db.commit()
            db.refresh(db_obj)

            self.logger.info(f"更新记录成功 ID={db_obj.id}")
            return db_obj
        except Exception as e:
            db.rollback()
            self.logger.error(f"更新记录失败 ID={db_obj.id}: {str(e)}")
            raise
    
    def delete(self, db: Session, id: Any) -> bool:
        """删除记录"""
        try:
            result = db.execute(delete(self.model).where(self.model.id == id))
            db.commit()

            deleted = result.rowcount > 0
            if deleted:
                self.logger.info(f"删除记录成功 ID={id}")
            else:
                self.logger.warning(f"删除记录失败，记录不存在 ID={id}")

            return deleted
        except Exception as e:
            db.rollback()
            self.logger.error(f"删除记录失败 ID={id}: {str(e)}")
            raise
    
    def count(
        self,
        db: Session,
        filters: Optional[Dict[str, Any]] = None
    ) -> int:
        """统计记录数量"""
        try:
            query = select(func.count(self.model.id))

            # 应用过滤条件
            if filters:
                for key, value in filters.items():
                    if hasattr(self.model, key) and value is not None:
                        query = query.where(getattr(self.model, key) == value)

            result = db.execute(query)
            return result.scalar()
        except Exception as e:
            self.logger.error(f"统计记录数量失败: {str(e)}")
            raise
    
    def exists(self, db: Session, id: Any) -> bool:
        """检查记录是否存在"""
        try:
            result = db.execute(
                select(self.model.id).where(self.model.id == id)
            )
            return result.scalar_one_or_none() is not None
        except Exception as e:
            self.logger.error(f"检查记录存在性失败 ID={id}: {str(e)}")
            raise
    
    def get_multi_with_total(
        self,
        db: Session,
        skip: int = 0,
        limit: int = 100,
        **filters
    ) -> tuple[List[ModelType], int]:
        """获取多个记录及总数"""
        try:
            # 构建基础查询
            query = select(self.model)
            count_query = select(func.count(self.model.id))

            # 应用过滤条件
            filter_conditions = self._build_filters(**filters)
            if filter_conditions:
                query = query.where(and_(*filter_conditions))
                count_query = count_query.where(and_(*filter_conditions))

            # 获取总数
            count_result = db.execute(count_query)
            total = count_result.scalar()

            # 获取数据
            query = query.offset(skip).limit(limit)
            data_result = db.execute(query)
            items = data_result.scalars().all()

            return items, total
        except Exception as e:
            self.logger.error(f"获取记录列表和总数失败: {str(e)}")
            raise

    def remove(self, db: Session, *, id: int) -> ModelType:
        """删除记录（返回被删除的对象）"""
        try:
            # 先获取对象
            obj = self.get(db, id)
            if not obj:
                raise ValueError(f"记录不存在 ID={id}")

            # 删除对象
            db.delete(obj)
            db.commit()

            self.logger.info(f"删除记录成功 ID={id}")
            return obj
        except Exception as e:
            db.rollback()
            self.logger.error(f"删除记录失败 ID={id}: {str(e)}")
            raise

    def _build_filters(self, **kwargs) -> List:
        """构建查询过滤条件"""
        filters = []
        for key, value in kwargs.items():
            if hasattr(self.model, key) and value is not None:
                filters.append(getattr(self.model, key) == value)
        return filters
