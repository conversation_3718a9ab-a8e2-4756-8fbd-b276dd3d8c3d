#!/bin/bash

# 燕友圈榜单系统 - 文档生成脚本
# 一键生成所有API文档和测试工具

set -e

echo "🚀 燕友圈榜单系统 - 文档生成工具"
echo "=================================="

# 检查Python环境
if ! command -v python &> /dev/null; then
    echo "❌ Python未安装或不在PATH中"
    exit 1
fi

# 检查项目依赖
echo "📦 检查项目依赖..."
if [ ! -f "requirements.txt" ]; then
    echo "❌ 未找到requirements.txt文件"
    exit 1
fi

# 创建docs目录
echo "📁 创建文档目录..."
mkdir -p docs

# 生成OpenAPI文档
echo "📚 生成OpenAPI文档..."
if python scripts/generate_api_docs.py; then
    echo "✅ OpenAPI文档生成成功"
else
    echo "❌ OpenAPI文档生成失败"
    exit 1
fi

# 生成Postman集合
echo "📮 生成Postman集合..."
if python scripts/generate_postman_collection.py; then
    echo "✅ Postman集合生成成功"
else
    echo "❌ Postman集合生成失败"
    exit 1
fi

# 测试API端点（可选）
echo ""
read -p "是否要测试API端点？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔍 测试API端点..."
    if python scripts/test_api_endpoints.py; then
        echo "✅ API端点测试完成"
    else
        echo "⚠️ API端点测试发现问题，请检查服务是否运行"
    fi
fi

# 显示生成的文件
echo ""
echo "📋 生成的文档文件："
echo "├── docs/"
echo "│   ├── API_COMPLETE_DOCUMENTATION.md  (完整API文档)"
echo "│   ├── README.md                      (文档说明)"
echo "│   ├── openapi.json                   (OpenAPI规范)"
echo "│   └── postman_collection.json        (Postman集合)"
echo "└── scripts/"
echo "    ├── generate_api_docs.py           (文档生成器)"
echo "    ├── test_api_endpoints.py          (端点测试器)"
echo "    └── generate_postman_collection.py (Postman生成器)"

echo ""
echo "🎉 文档生成完成！"
echo ""
echo "📖 使用方法："
echo "1. 查看API文档：docs/API_COMPLETE_DOCUMENTATION.md"
echo "2. 导入Postman集合：docs/postman_collection.json"
echo "3. 查看详细说明：docs/README.md"
echo ""
echo "🔧 维护命令："
echo "- 重新生成文档：./generate_docs.sh"
echo "- 测试API端点：python scripts/test_api_endpoints.py"
echo "- 更新Postman集合：python scripts/generate_postman_collection.py"
