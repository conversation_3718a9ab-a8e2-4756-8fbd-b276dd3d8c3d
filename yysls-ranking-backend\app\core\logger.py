"""
日志配置模块
"""
import logging
import os
from datetime import datetime
from typing import Optional

from app.config import settings


def setup_logging():
    """设置日志配置"""
    # 创建日志目录
    log_dir = os.path.dirname(settings.log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # 配置日志格式
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"
    
    # 配置根日志器
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format=log_format,
        datefmt=date_format,
        handlers=[
            # 控制台输出
            logging.StreamHandler(),
            # 文件输出
            logging.FileHandler(settings.log_file, encoding='utf-8')
        ]
    )
    
    # 设置第三方库的日志级别
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取日志器实例
    
    Args:
        name: 日志器名称，通常使用 __name__
        
    Returns:
        logging.Logger: 日志器实例
    """
    if name is None:
        name = __name__
    
    logger = logging.getLogger(name)
    
    # 如果是第一次获取这个日志器，设置其级别
    if not logger.handlers:
        logger.setLevel(getattr(logging, settings.log_level.upper()))
    
    return logger


# 全局日志器实例
logger = get_logger(__name__)


class LoggerMixin:
    """日志器混入类，为其他类提供日志功能"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志器"""
        return get_logger(self.__class__.__module__ + "." + self.__class__.__name__)


# 初始化日志配置
setup_logging()
